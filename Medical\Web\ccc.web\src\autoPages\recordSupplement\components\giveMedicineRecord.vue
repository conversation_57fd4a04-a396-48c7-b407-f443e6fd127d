<!--
 * FilePath     : \src\autoPages\recordSupplement\components\giveMedicineRecord.vue
 * Author       : AI Assistant
 * Date         : 2025-07-21
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-30 17:59
 * Description  : 给药记录补录页面
 * CodeIterationRecord:
 -->
<template>
  <div class="give-medicine-record-wrapper">
    <base-layout class="give-medicine-record" header-height="auto">
      <div slot="header" class="give-medicine-record-header">
        <div class="header-controls">
          <span class="label">日期:</span>
          <el-date-picker
            v-model="scheduleDate"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="选择日期"
            class="date-select"
            @change="getTableData"
          ></el-date-picker>
          <el-button
            type="primary"
            class="add-button give-medicine-record-header-add-button"
            @click="recordAddOrUpdate()"
            icon="iconfont icon-add"
          >
            新增
          </el-button>
        </div>
      </div>
      <div class="give-medicine-record-content">
        <el-table ref="giveMedicineTable" height="100%" :data="tableData" border stripe v-loading="loading">
          <el-table-column prop="scheduleDateTime" label="日期" :width="convertPX(160)" align="center">
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.scheduleDateTime, type: 'dateTime' }"></span>
            </template>
          </el-table-column>
          <el-table-column prop="orderType" label="类别" :width="convertPX(80)" align="center"></el-table-column>
          <el-table-column prop="hisGroupID" label="组号" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="orderContent" label="医嘱内容" :min-width="convertPX(200)"></el-table-column>
          <el-table-column prop="orderRule" label="途径" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="speed" label="泵速/滴速" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="frequency" label="频次" :width="convertPX(80)" align="center"></el-table-column>
          <el-table-column prop="doctor" label="医师" :width="convertPX(100)" align="center"></el-table-column>
          <el-table-column prop="performDateTime" label="执行时间" :width="convertPX(140)" align="center">
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.performDateTime, type: 'dateTime' }"></span>
            </template>
          </el-table-column>
          <el-table-column prop="endDateTime" label="结束时间" :width="convertPX(140)" align="center">
            <template slot-scope="scope">
              <span v-formatTime="{ value: scope.row.endDateTime, type: 'dateTime' }"></span>
            </template>
          </el-table-column>
          <el-table-column
            prop="performEmployee"
            label="执行人员"
            :width="convertPX(100)"
            align="center"
          ></el-table-column>
          <el-table-column label="操作" header-align="center" :width="convertPX(120)" align="center" fixed="right">
            <template slot-scope="scope">
              <el-tooltip content="修改">
                <i class="iconfont icon-edit" @click="recordAddOrUpdate(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i @click="giveMedicineRecordDelete(scope.row)" class="iconfont icon-del"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </base-layout>
    <!-- 新增/修改抽屉 -->
    <el-drawer
      :append-to-body="false"
      :title="dialogTitle"
      :visible.sync="showDrawer"
      direction="btt"
      size="70%"
      :close-on-click-modal="false"
      :wrapperClosable="false"
      custom-class="give-medicine-record-drawer"
    >
      <div class="drawer-content">
        <base-layout headerHeight="auto">
          <div slot="header">
            <station-department-bed-date
              v-if="showDrawer"
              :switch="componentsSwitch"
              :stDeptBed="saveView"
              :enteredStationList="enteredStationList"
              @custCkick="getComponentData"
            />
            <user-selector
              v-model="saveView.addEmployeeID"
              :stationID="saveView.stationID"
              label="执行人："
              clearable
              filterable
              remoteSearch
              :width="convertPX(199) + 'px'"
              :disabled="disabledFlag"
            />
          </div>
          <el-form
            ref="giveMedicineForm"
            :model="saveView"
            :rules="formRules"
            label-width="100px"
            label-position="right"
          >
            <el-form-item label="日期：" prop="performDate">
              <el-date-picker
                v-model="saveView.scheduleDateTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="类别：" prop="orderType">
              <el-select v-model="saveView.orderType" placeholder="请选择类别">
                <el-option
                  v-for="item in orderTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="组号：" prop="hisGroupID">
              <el-input v-model="saveView.hisGroupID" placeholder="请输入组号"></el-input>
            </el-form-item>
            <el-form-item label="医嘱内容：" prop="orderContent">
              <el-input
                v-model="saveView.orderContent"
                type="textarea"
                :rows="3"
                placeholder="请输入医嘱内容"
              ></el-input>
            </el-form-item>
            <el-form-item label="途径：" prop="orderRule">
              <el-input v-model="saveView.orderRule" placeholder="请输入途径"></el-input>
            </el-form-item>
            <el-form-item label="剂量/滴速：" prop="speed">
              <el-input v-model="saveView.speed" placeholder="请输入剂量/滴速"></el-input>
            </el-form-item>
            <el-form-item label="频次：" prop="frequencyID">
              <frequency-selector
                v-model="saveView.frequencyID"
                :frequencyList="frequencyList"
                v-if="frequencyList && frequencyList.length > 0"
              ></frequency-selector>
            </el-form-item>
            <el-form-item label="医师：" prop="doctor">
              <el-input v-model="saveView.doctor" placeholder="请输入医师"></el-input>
            </el-form-item>
            <el-form-item label="执行时间：" prop="performDateTime">
              <el-date-picker
                v-model="saveView.performDateTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                placeholder="选择时间"
                :disabled="!canEditPerformTime"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间：" prop="endDateTime">
              <el-date-picker
                v-model="saveView.endDateTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                placeholder="选择时间"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </base-layout>
      </div>
      <div class="drawer-footer">
        <el-button @click="showDrawer = false">取消</el-button>
        <el-button type="primary" @click="saveGiveMedicineRecord" :disabled="!saveFlag">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import baseLayout from "@/components/BaseLayout";
import userSelector from "@/components/selector/userSelector";
import frequencySelector from "@/components/selector/frequencySelector";
import stationDepartmentBedDate from "@/pages/recordSupplement/components/stationDepartmentBedDate.vue";

import { GetTypeFrequency } from "@/api/Frequency";
import { mapGetters } from "vuex";
import { GetGiveMedicineRecordTableData, UpdatePatientMedicineSchedule } from "@/api/GiveMedicineRecord";
import { GetClinicalSettingInfo } from "@/api/Assess";
import { GetStationList } from "@/api/Station";
export default {
  components: {
    baseLayout,
    userSelector,
    frequencySelector,
    stationDepartmentBedDate,
  },
  props: {
    supplementPatient: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters({
      user: "getUser",
    }),
    /**
     * description: 是否可以编辑执行时间
     * return {Boolean}
     */
    canEditPerformTime() {
      return this.saveView.orderRefillFlag === true;
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      scheduleDate: this._datetimeUtil.getNowDate("yyyy-MM-dd"),
      dialogTitle: "",
      showDrawer: false,
      disabledFlag: false,
      saveFlag: true,
      formRules: {
        performDate: [{ required: true, message: "请选择执行日期", trigger: "change" }],
        performTime: [{ required: true, message: "请选择执行时间", trigger: "change" }],

      },
      saveView: {
        patientMedicineScheduleID: "",
        inpatientID: "",
        performDate:"",
        performTime: undefined
        hisGroupID: "",
        orderContent: "",
        orderRule: "",
        speed: "",
        frequencyID: "",
        doctor: "",
        endDateTime: "",
        addEmployeeID: "",
        orderRefillFlag: false,
      },
      frequencyList: [],
      orderTypeOptions: [],
      // 设置病区科室床位日期组件是否显示
      componentsSwitch: {
        stationSwitch: true,
        departmentListSwitch: true,
        bedNumberSwitch: true,
      },
      enteredStationList: [],
      defaultStationID: "",
      defaultDepartmentListID: "",
    };
  },
  mounted() {
    this.getTableData();
    this.getTypeFrequency();
    this.getOrderTypeOptions();
    this.getEnteredStationList();
  },
  methods: {
    /**
     * description: 获取表格数据
     * return {*}
     */
    getTableData() {
      if (!this.supplementPatient || !this.supplementPatient.inpatientID) {
        return;
      }
      this.loading = true;
      const params = {
        inpatientID: this.supplementPatient.inpatientID,
        scheduleDate: this.scheduleDate,
      };
      GetGiveMedicineRecordTableData(params).then((res) => {
        this.loading = false;
        if (this._common.isSuccess(res)) {
          this.tableData = res.data || [];
        }
      });
    },
    /**
     * description: 新增/修改点击弹窗
     * param {*} row
     * return {*}
     */
    recordAddOrUpdate(row) {
      if (row) {
        this.dialogTitle = "修改给药记录";
        this.saveView = {
          patientMedicineScheduleID: row.patientMedicineScheduleID,
          inpatientID: row.inpatientID,
          performDateTime: row.performDateTime,
        };
      } else {
        this.dialogTitle = "新增给药记录";
        this.saveView = {
          patientMedicineScheduleID: undefined,
          inpatientID: this.supplementPatient.inpatientID,
          performDateTime: undefined,
        };
      }
      this.showDrawer = true;
    },
    /**
     * description: 保存给药记录
     * return {*}
     */
    saveGiveMedicineRecord() {
      this.$refs.giveMedicineForm.validate((valid) => {
        if (valid) {
          UpdatePatientMedicineSchedule(this.saveView).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "保存成功!");
              this.showDrawer = false;
              this.getTableData();
            }
          });
        } else {
          this._showTip("warning", "请完善必填信息");
          return false;
        }
      });
    },
    /**
     * description: 删除给药记录
     * param {*} row
     * return {*}
     */
    deleteGiveMedicineRecord(row) {
      let _this = this;
      _this._deleteConfirm("", (flag) => {
        if (flag) {
          const params = {
            patientMedicineScheduleID: row.patientMedicineScheduleID,
          };
          DeleteGiveMedicineRecord(params).then((res) => {
            if (this._common.isSuccess(res)) {
              this._showTip("success", "删除成功");
              this.getTableData();
            }
          });
        }
      });
    },
    /**
     * description: 获取频次列表
     * return {*}
     */
    async getTypeFrequency() {
      this.frequencyList = [];
      let params = {
        visibleFlag: true,
      };
      await GetTypeFrequency(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.frequencyList = result.data;
        }
      });
    },
    /**
     * @description: 获取医嘱类别配置
     * @return {*}
     */
    async getOrderTypeOptions() {
      //从配置获取延续护理类别
      let params = {
        SettingTypeCode: "OrderType",
      };
      await GetClinicalSettingInfo(params).then((res) => {
        if (this._common.isSuccess(res)) {
          this.orderTypeOptions = res.data.map((option) => {
            return {
              label: option.description,
              value: option.typeValue,
            };
          });
        }
      });
    },
    /**
     * description: 获取患者经历病区列表
     * return {*}
     */
    async getEnteredStationList() {
      let params = {
        inpatientID: this.supplementPatient.inpatientID,
      };
      await GetStationList(params).then((result) => {
        if (this._common.isSuccess(result)) {
          this.enteredStationList = result.data;
          let filterData = result.data.find((station) => station.id == this.user.stationID);
          if (filterData) {
            this.defaultStationID = filterData.id || undefined;
            this.defaultDepartmentListID = filterData.departmentListID || undefined;
          }
        }
      });
    },
    /**
     * description: 回调更新对象成员
     * param {*} tempStDeptBed
     * return {*}
     */
    getComponentData(tempStDeptBed) {
      this.saveView.stationID = tempStDeptBed.stationID;
      this.saveView.departmentListID = tempStDeptBed.departmentListID;
      this.saveView.bedNumber = tempStDeptBed.bedNumber;
      this.saveView.bedID = tempStDeptBed.bedId;
    },
  },
};
</script>
<style lang="scss">
.give-medicine-record-wrapper {
  height: 100%;
  .give-medicine-record {
    height: 100%;
    .give-medicine-record-header {
      .header-controls {
        display: flex;
        align-items: center;
        .label {
          margin-right: 10px;
          font-weight: bold;
        }
        .date-select {
          width: 155px;
          margin-right: 20px;
        }
        .give-medicine-record-header-add-button {
          float: right;
          margin-left: auto;
        }
      }
    }
    .give-medicine-record-content {
      height: 100%;
      .el-table {
        .el-table__row td:not(.is-hidden):last-child {
          border-left: 1px solid #ebeef5;
        }
        .el-table__header th:not(.is-hidden):last-child {
          border-left: 1px solid #ebeef5;
        }
      }
    }
  }
  .give-medicine-record-drawer {
    height: 100%;
    .el-drawer__body {
      height: calc(100% - 55px);
      .drawer-content {
        height: calc(100% - 53px);
      }
      .drawer-footer {
        flex-shrink: 0;
        padding: 15px 20px;
        text-align: right;
        background: #fff;
        border-top: 1px solid #e4e7ed;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        z-index: 10;
      }
    }
  }
}
</style>