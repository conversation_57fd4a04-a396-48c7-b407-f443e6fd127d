/*
 * FilePath     : \src\api\GiveMedicineRecord.js
 * Author       : AI Assistant
 * Date         : 2025-07-21
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-25 15:40
 * Description  : 给药记录补录API
 */
import http from "../utils/ajax";

const baseUrl = "/GiveMedicineSupplement";
export const urls = {
  GetGiveMedicineRecordTableData: baseUrl + "/GetGiveMedicineRecordTableData",
  UpdatePatientMedicineSchedule: baseUrl + "/UpdatePatientMedicineSchedule",
  DeleteGiveMedicineRecord: baseUrl + "/DeleteGiveMedicineRecord",
};
/**
 * description: 获取给药记录表格数据
 * param {Object} params - 查询参数
 * return {Promise}
 */
export const GetGiveMedicineRecordTableData = (params) => {
  return http.get(urls.GetGiveMedicineRecordTableData, params);
};
/**
 * description: 保存给药记录
 * param {Object} params - 保存参数
 * return {Promise}
 */
export const UpdatePatientMedicineSchedule = (params) => {
  return http.post(urls.UpdatePatientMedicineSchedule, params);
};
